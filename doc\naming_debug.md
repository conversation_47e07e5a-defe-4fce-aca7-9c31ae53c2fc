# Context Vault Naming & Debug Protocol

## Overview
This document tracks the step-by-step debugging process for implementing smart context vault naming and folder management in ChatLo.

## Smart Naming System Design

### Current vs Proposed Naming
- **Current**: `ctx_your-first-personal-context_1a2b3c4d5`
- **Proposed**: Human-readable names with metadata tracking
  - Display Name: "Your First Personal Context"
  - Internal ID: Generated UUID stored in `./context/metadata.json`
  - Folder Structure: `<vault_root>/<display_name>/`

### Metadata Structure (example)
```json
{
  "contexts": {
    "uuid-1a2b3c4d5": {
      "id": "uuid-1a2b3c4d5",
      "displayName": "Your First Personal Context",
      "folderPath": "/path/to/vault/Your First Personal Context",
      "created": "2025-07-29T15:45:00Z",
      "updated": "2025-07-29T15:45:00Z",
      "type": "personal",
      "description": "Initial personal context for general use"
    },
    "uuid-2b3c4d5e6": {
      "id": "uuid-2b3c4d5e6", 
      "displayName": "Your First Work Project",
      "folderPath": "/path/to/vault/Your First Work Project",
      "created": "2025-07-29T15:45:00Z",
      "updated": "2025-07-29T15:45:00Z",
      "type": "work",
      "description": "Initial work project context"
    }
  },
  "version": "1.0",
  "lastUpdated": "2025-07-29T15:45:00Z"
}
```

## Debug Steps Protocol

### Step 1: New Folder Creation → Context ID Creation → Trigger Folder Scan → Expose Path
**Status**: IN PROGRESS
**Action**: Implementing smart naming system

**Expected Behavior**:
1. User creates new context vault folder
2. System generates UUID for context
3. System creates metadata.json entry with smart names:
   - "getting started" → "Your First Personal Context"
   - "project" → "Your First Work Project"
4. System scans folder structure
5. System exposes folder path to frontend

**Debug Points**:
- [Y ] UUID generation working
- [ ] Metadata.json creation/update
- [ ] Smart name mapping implemented
- [ ] Folder scan triggered
- [ ] Path exposure to frontend

**CURRENT WORK**: Implementing name mapping logic

---

### Step 2: Same Folder Added to Settings Again → Check Consistency → No Duplication → Trigger Folder Scan
**Status**: Not Started
**Action**: Awaiting Step 1 completion

**Expected Behavior**:
1. User attempts to add existing folder
2. System checks metadata.json for existing context ID
3. System prevents duplication
4. System maintains consistency
5. System triggers folder scan to locate file paths

**Debug Points**:
- [ ] Duplicate detection working
- [ ] Context ID consistency maintained
- [ ] No folder duplication in UI
- [ ] File path location accurate

---

### Step 3: Expose Path After Folder Restored
**Status**: Not Started
**Action**: Awaiting previous steps

**Expected Behavior**:
1. System restores context from metadata
2. System validates folder path exists
3. System exposes restored path to frontend
4. System maintains context state

**Debug Points**:
- [ ] Path restoration working
- [ ] Folder validation successful
- [ ] Frontend path exposure
- [ ] State consistency maintained

---

### Step 4: Folder Path Storage Method
**Status**: Not Started
**Action**: Awaiting analysis

**Investigation Points**:
- How to store folder paths (absolute vs relative)
- Database vs file-based storage
- Cross-platform compatibility
- Path validation and error handling

---

### Step 5: API Endpoint Exposure → Token Issued → Frontend Card Design → File Drop
**Status**: Not Started
**Action**: Awaiting implementation

**Expected Behavior**:
1. API endpoints expose folder paths
2. Security tokens issued for access
3. Frontend cards display context vaults
4. File drop functionality on homepage cards

**Debug Points**:
- [ ] API endpoints working
- [ ] Token security implemented
- [ ] Frontend cards rendering
- [ ] File drop functionality

---

### Step 6: Folder File Health Check
**Status**: Not Started
**Action**: Awaiting user verification

**Expected Behavior**:
1. User navigates to physical folder
2. Files are properly organized
3. Metadata consistency verified
4. File health status confirmed

**Debug Points**:
- [ ] Physical folder structure correct
- [ ] Files properly indexed
- [ ] Metadata accuracy
- [ ] No corruption or missing files

---

## Current Status
**Waiting for**: `/check_step` command to begin Step 1

## Notes
- Each step will be documented with actual results vs expected behavior
- Debug findings will be added as we progress
- Code changes will be tracked with file references
- Issues and solutions will be documented for future reference

---

*Last Updated: 2025-07-29 - Initial setup complete, awaiting debug session start*
