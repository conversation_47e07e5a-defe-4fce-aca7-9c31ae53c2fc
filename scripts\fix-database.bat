@echo off
echo ChatLo Database Repair Utility
echo ===============================
echo.

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%.."

echo Current directory: %CD%
echo.

echo Available options:
echo 1. Fix production database
echo 2. Fix development database  
echo 3. Fix production database with backup
echo 4. Fix development database with backup
echo 5. Force full repair (production)
echo 6. Force full repair (development)
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo Running repair for production database...
    node scripts/fix-database.js
) else if "%choice%"=="2" (
    echo Running repair for development database...
    node scripts/fix-database.js --dev
) else if "%choice%"=="3" (
    echo Running repair for production database with backup...
    node scripts/fix-database.js --backup
) else if "%choice%"=="4" (
    echo Running repair for development database with backup...
    node scripts/fix-database.js --dev --backup
) else if "%choice%"=="5" (
    echo Running force full repair for production database...
    node scripts/fix-database.js --force --backup
) else if "%choice%"=="6" (
    echo Running force full repair for development database...
    node scripts/fix-database.js --dev --force --backup
) else (
    echo Invalid choice. Please run the script again.
    goto end
)

echo.
echo Repair completed. Check the output above for results.

:end
echo.
pause
