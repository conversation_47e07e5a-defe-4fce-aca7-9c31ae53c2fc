#!/usr/bin/env node

/**
 * Database Repair Utility for ChatLo
 * 
 * This script helps fix database corruption issues, particularly
 * index corruption like "wrong # of entries in index idx_messages_conversation_id"
 * 
 * Usage: node scripts/fix-database.js [options]
 * Options:
 *   --dev     Use development database
 *   --backup  Create backup before repair
 *   --force   Force full repair instead of index-only repair
 */

const Database = require('better-sqlite3')
const path = require('path')
const fs = require('fs')

// Parse command line arguments
const args = process.argv.slice(2)
const isDev = args.includes('--dev')
const createBackup = args.includes('--backup')
const forceFullRepair = args.includes('--force')

// Determine database path
const dbPath = isDev 
  ? path.join(__dirname, '../chatlo-dev.db')
  : path.join(require('os').homedir(), 'AppData/Roaming/chatlo/chatlo.db')

console.log(`ChatLo Database Repair Utility`)
console.log(`Database path: ${dbPath}`)
console.log(`Options: dev=${isDev}, backup=${createBackup}, force=${forceFullRepair}`)
console.log('---')

if (!fs.existsSync(dbPath)) {
  console.error('❌ Database file not found!')
  process.exit(1)
}

let db

try {
  // Open database
  console.log('📂 Opening database...')
  db = new Database(dbPath)
  
  // Create backup if requested
  if (createBackup) {
    const backupPath = dbPath + '.backup.' + Date.now()
    fs.copyFileSync(dbPath, backupPath)
    console.log(`💾 Backup created: ${backupPath}`)
  }

  // Check current integrity
  console.log('🔍 Checking database integrity...')
  const integrityResult = db.prepare('PRAGMA integrity_check').get()
  
  if (integrityResult.integrity_check === 'ok') {
    console.log('✅ Database integrity is OK - no repair needed!')
    process.exit(0)
  }

  console.log(`❌ Integrity check failed: ${integrityResult.integrity_check}`)

  // Try index repair first (unless force full repair)
  if (!forceFullRepair) {
    console.log('🔧 Attempting index repair...')
    
    const indexesToRepair = [
      'idx_messages_conversation_id',
      'idx_messages_created_at', 
      'idx_artifacts_message_id',
      'idx_messages_conversation_created',
      'idx_files_hash_type',
      'idx_artifacts_message_type',
      'idx_conversations_updated',
      'idx_file_attachments_message',
      'idx_file_attachments_file',
      'idx_pinned_intelligence_message_id'
    ]

    // Drop problematic indexes
    for (const indexName of indexesToRepair) {
      try {
        db.exec(`DROP INDEX IF EXISTS ${indexName}`)
        console.log(`  ✓ Dropped index: ${indexName}`)
      } catch (error) {
        console.log(`  ⚠️  Could not drop index ${indexName}: ${error.message}`)
      }
    }

    // Recreate indexes
    console.log('🔨 Recreating indexes...')
    try {
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
        CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
        CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts (message_id);
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_created ON messages (conversation_id, created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_files_hash_type ON files (content_hash, file_type);
        CREATE INDEX IF NOT EXISTS idx_artifacts_message_type ON artifacts (message_id, type);
        CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations (updated_at DESC);
        CREATE INDEX IF NOT EXISTS idx_file_attachments_message ON file_attachments (message_id);
        CREATE INDEX IF NOT EXISTS idx_file_attachments_file ON file_attachments (file_id);
        CREATE INDEX IF NOT EXISTS idx_pinned_intelligence_message_id ON pinned_intelligence (message_id);
      `)
      console.log('  ✅ All indexes recreated successfully')
    } catch (error) {
      console.error('  ❌ Index recreation failed:', error.message)
      console.log('  🔄 Will attempt full database repair...')
      forceFullRepair = true
    }

    // Check integrity again
    if (!forceFullRepair) {
      console.log('🔍 Re-checking integrity after index repair...')
      const recheckResult = db.prepare('PRAGMA integrity_check').get()
      
      if (recheckResult.integrity_check === 'ok') {
        console.log('✅ Database integrity restored! Running VACUUM to optimize...')
        db.exec('VACUUM')
        console.log('🎉 Database repair completed successfully!')
        process.exit(0)
      } else {
        console.log(`❌ Integrity still failed: ${recheckResult.integrity_check}`)
        console.log('🔄 Will attempt full database repair...')
      }
    }
  }

  // Full database repair (last resort)
  console.log('⚠️  Attempting full database repair - this may take longer...')
  console.log('📊 Analyzing database structure...')
  
  // Get table info
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all()
  console.log(`Found ${tables.length} tables: ${tables.map(t => t.name).join(', ')}`)

  // Run VACUUM to try to fix corruption
  console.log('🧹 Running VACUUM...')
  try {
    db.exec('VACUUM')
    console.log('  ✅ VACUUM completed')
  } catch (error) {
    console.error('  ❌ VACUUM failed:', error.message)
  }

  // Final integrity check
  console.log('🔍 Final integrity check...')
  const finalResult = db.prepare('PRAGMA integrity_check').get()
  
  if (finalResult.integrity_check === 'ok') {
    console.log('🎉 Database repair completed successfully!')
  } else {
    console.error(`❌ Database repair failed: ${finalResult.integrity_check}`)
    console.log('💡 You may need to restore from a backup or recreate the database')
    process.exit(1)
  }

} catch (error) {
  console.error('💥 Repair script failed:', error.message)
  console.error(error.stack)
  process.exit(1)
} finally {
  if (db) {
    db.close()
  }
}
