import React, { useState, useRef, useEffect } from 'react'
import { useAppStore } from '../store'
import { Send, Plus, Wrench, Loader2 } from './Icons'
import ChatSettingsDrawer from './ChatSettingsDrawer'
import AttachmentMenu from './AttachmentMenu'
import FilePicker from './FilePicker'
import FileAutocomplete from './FileAutocomplete'
import FileAttachments from './FileAttachments'
import { FileRecord } from '../types'
import { validateImageFile, validateImageDimensions, resizeImage, shouldOptimizeImage } from '../utils/imageUtils'
import { useArtifactToasts } from './artifacts/controls/ArtifactToast'
import { CompactVaultSelector } from './CompactVaultSelector'
import { sharedDropboxService } from '../services/sharedDropboxService'



// Helper function to convert file to base64 safely
const fileToBase64 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // Remove data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

const InputArea: React.FC = () => {
  const { currentConversationId, sendMessage, isLoading, models, settings } = useAppStore()
  const toasts = useArtifactToasts()
  const [input, setInput] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
  const [showFilePicker, setShowFilePicker] = useState(false)
  const [filePickerMode, setFilePickerMode] = useState<'files' | 'images'>('files')
  const [attachedFiles, setAttachedFiles] = useState<FileRecord[]>([])
  const [showAutocomplete, setShowAutocomplete] = useState(false)
  const [autocompleteQuery, setAutocompleteQuery] = useState('')
  const [autocompletePosition, setAutocompletePosition] = useState({ top: 0, left: 0 })
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)
  const [cursorPosition, setCursorPosition] = useState(0)

  const [isDragOver, setIsDragOver] = useState(false)
  const [, setDragCounter] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const attachButtonRef = useRef<HTMLButtonElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleContextChange = (contextId: string | null) => {
    setSelectedContextId(contextId)
    console.log('Context selected for auto-classification:', contextId)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentConversationId || isLoading) return

    const message = input.trim()
    setInput('')

    // Clear attached files after sending
    const filesToSend = [...attachedFiles]
    setAttachedFiles([])

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    try {
      // TODO: Update sendMessage to handle file attachments and context classification
      await sendMessage(message, currentConversationId, filesToSend, selectedContextId)
    } catch (error) {
      console.error('Error sending message:', error)
      // The error will be handled by the store and displayed in the chat
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Don't handle Enter if autocomplete is open (let autocomplete handle it)
    if (showAutocomplete && (e.key === 'Enter' || e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'Escape')) {
      return
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [input])

  // Optimized clipboard paste handler for images
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // Only handle paste if input area is focused
      if (document.activeElement !== textareaRef.current) return

      const items = e.clipboardData?.items
      if (!items) return

      for (let i = 0; i < items.length; i++) {
        const item = items[i]

        if (item.type.startsWith('image/')) {
          e.preventDefault()

          try {
            const file = item.getAsFile()
            if (!file) continue

            // Create a unique filename
            const timestamp = Date.now()
            const extension = file.type.split('/')[1] || 'png'
            const filename = `pasted-image-${timestamp}.${extension}`

            // Convert file to base64 and save to uploads
            const base64String = await fileToBase64(file)

            if (window.electronAPI?.files) {
              // Save the image to the uploads folder
              const tempPath = await window.electronAPI.files.saveContentAsFile(
                base64String,
                filename,
                'Uploads'
              )

              // Index the file (metadata only)
              const fileId = await window.electronAPI.files.indexFile(tempPath)

              if (fileId) {
                // Get the file record and add to attachments
                const files = await window.electronAPI.files.getIndexedFiles()
                const newFile = files.find(f => f.id === fileId)
                if (newFile) {
                  setAttachedFiles(prev => [...prev, newFile])
                }
              }
            }
          } catch (error) {
            console.error('Error processing pasted image:', error)
          }

          break // Only process the first image
        }
      }
    }

    document.addEventListener('paste', handlePaste)
    return () => document.removeEventListener('paste', handlePaste)
  }, [])

  const handleFileSelect = () => {
    console.log('File select clicked')
    setFilePickerMode('files')
    setShowFilePicker(true)
  }

  const handleImageSelect = () => {
    console.log('Image select clicked')
    setFilePickerMode('images')
    setShowFilePicker(true)
  }

  const handleDroppedFiles = async (files: File[]) => {
    console.log('Files dropped:', files.length)

    try {
      const processedFiles: FileRecord[] = []

      for (const file of files) {
        // Check file type
        const isImage = file.type.startsWith('image/')
        const isPDF = file.type === 'application/pdf'
        const isText = file.type.startsWith('text/')
        const isWord = file.type.includes('word') || file.name.endsWith('.docx')
        const isExcel = file.type.includes('sheet') || file.name.endsWith('.xlsx')
        const isPowerPoint = file.type.includes('presentation') || file.name.endsWith('.pptx')

        if (!isImage && !isPDF && !isText && !isWord && !isExcel && !isPowerPoint) {
          toasts.error(`Unsupported file type: ${file.name}`)
          continue
        }

        // Validate images
        if (isImage) {
          const validation = validateImageFile(file)
          if (!validation.isValid) {
            toasts.error(`${file.name}: ${validation.error}`)
            continue
          }

          // Check dimensions
          const dimensionValidation = await validateImageDimensions(file)
          if (!dimensionValidation.isValid) {
            toasts.error(`${file.name}: ${dimensionValidation.error}`)
            continue
          }

          // Show warnings if any
          if (validation.warnings) {
            validation.warnings.forEach(warning => {
              toasts.error(`${file.name}: ${warning}`)
            })
          }
        }

        // Optimize image if needed
        let fileToProcess = file
        if (isImage && shouldOptimizeImage(file)) {
          try {
            const optimized = await resizeImage(file, {
              maxWidth: 2048,
              maxHeight: 2048,
              quality: 0.85
            })
            fileToProcess = optimized.file
            toasts.success(`${file.name} optimized: ${Math.round((1 - optimized.newSize / optimized.originalSize) * 100)}% size reduction`)
          } catch (error) {
            console.warn('Image optimization failed, using original:', error)
          }
        }

        // Use shared dropbox service for context-aware upload
        const uploadResult = await sharedDropboxService.uploadFile(fileToProcess)

        if (uploadResult.success && uploadResult.fileRecord) {
          // Convert SharedDropboxFile to FileRecord format
          const fileRecord: FileRecord = {
            id: uploadResult.fileRecord.id,
            filename: uploadResult.fileRecord.filename,
            file_path: uploadResult.fileRecord.filepath,
            file_type: uploadResult.fileRecord.fileType,
            file_size: uploadResult.fileRecord.fileSize,
            file_hash: '', // Not used in new system
            indexed_at: uploadResult.fileRecord.uploadedAt,
            extracted_content: uploadResult.fileRecord.extractedContent || null,
            metadata: {
              uploaded_via: 'drag_drop',
              processed: uploadResult.fileRecord.processed
            }
          }

          processedFiles.push(fileRecord)

          // Get upload destination info for user feedback
          const destination = await sharedDropboxService.getUploadDestination()
          const destinationName = destination.type === 'context'
            ? `context vault "${destination.contextName}"`
            : 'shared dropbox'

          console.log(`[Upload] ${file.name} saved to ${destinationName}`)
        } else {
          toasts.error(`Failed to upload ${file.name}: ${uploadResult.error}`)
        }
      }

      if (processedFiles.length > 0) {
        setAttachedFiles(prev => [...prev, ...processedFiles])

        // Show success message with destination info
        const destination = await sharedDropboxService.getUploadDestination()
        const destinationName = destination.type === 'context'
          ? `context vault "${destination.contextName}"`
          : 'shared dropbox'

        toasts.success(`${processedFiles.length} file(s) uploaded to ${destinationName}!`)

        // Auto-vectorize dropped files if needed
        for (const file of processedFiles) {
          if (!file.extracted_content) {
            await handleVectorizeFile(file.id)
          }
        }
      }
    } catch (error) {
      console.error('Error processing dropped files:', error)
      toasts.error('Error processing dropped files')
    }
  }

  const handleFilesSelected = (files: FileRecord[]) => {
    console.log('Files selected:', files);
    setAttachedFiles(prev => [...prev, ...files]);

    // Auto-vectorize selected files if needed
    for (const file of files) {
      if (!file.extracted_content) {
        handleVectorizeFile(file.id);
      }
    }
  };

  const handleVectorizeFile = async (fileId: string) => {
    console.log('Vectorizing file:', fileId)
    try {
      if (window.electronAPI?.files) {
        console.log('Processing file content...')
        const success = await window.electronAPI.files.processFileContent(fileId)
        console.log('Processing result:', success)

        if (success) {
          // Refresh the file data
          const files = await window.electronAPI.files.getIndexedFiles()
          const updatedFile = files.find(f => f.id === fileId)
          console.log('Updated file:', updatedFile)

          if (updatedFile) {
            setAttachedFiles(prev => prev.map(f => f.id === fileId ? updatedFile : f))
            toasts.success(`File "${updatedFile.filename}" has been vectorized successfully!`)
          }
        } else {
          toasts.error('Failed to vectorize file. Please try again.')
        }
      } else {
        console.error('ElectronAPI not available')
        toasts.error('ElectronAPI not available.')
      }
    } catch (error) {
      console.error('Error vectorizing file:', error)
      toasts.error('Error occurred while vectorizing file.')
    }
  }



  // Debounce autocomplete to avoid excessive API calls
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    const cursorPos = e.target.selectionStart
    setInput(value)
    setCursorPosition(cursorPos)

    // Clear previous debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    // Check for @ symbol for file autocomplete
    const textBeforeCursor = value.substring(0, cursorPos)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const query = atMatch[1]

      // Debounce the autocomplete to avoid excessive searches
      debounceTimeoutRef.current = setTimeout(() => {
        setAutocompleteQuery(query)

        // Simple positioning - show above textarea
        if (textareaRef.current) {
          const rect = textareaRef.current.getBoundingClientRect()
          setAutocompletePosition({
            top: rect.top - 250, // Show above the textarea
            left: rect.left
          })
          setShowAutocomplete(true)
        }
      }, 150) // 150ms debounce
    } else {
      setShowAutocomplete(false)
    }
  }

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  // Drag & Drop functionality
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragCounter(prev => prev + 1)
      if (e.dataTransfer?.types.includes('Files')) {
        setIsDragOver(true)
      }
    }

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragCounter(prev => {
        const newCounter = prev - 1
        if (newCounter === 0) {
          setIsDragOver(false)
        }
        return newCounter
      })
    }

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }

    const handleDrop = async (e: DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragOver(false)
      setDragCounter(0)

      const files = Array.from(e.dataTransfer?.files || [])
      if (files.length > 0) {
        await handleDroppedFiles(files)
      }
    }

    container.addEventListener('dragenter', handleDragEnter)
    container.addEventListener('dragleave', handleDragLeave)
    container.addEventListener('dragover', handleDragOver)
    container.addEventListener('drop', handleDrop)

    return () => {
      container.removeEventListener('dragenter', handleDragEnter)
      container.removeEventListener('dragleave', handleDragLeave)
      container.removeEventListener('dragover', handleDragOver)
      container.removeEventListener('drop', handleDrop)
    }
  }, [])

  const handleFileAutocompleteSelect = (file: FileRecord) => {
    const textBeforeCursor = input.substring(0, cursorPosition)
    const textAfterCursor = input.substring(cursorPosition)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const beforeAt = textBeforeCursor.substring(0, atMatch.index)
      const newText = beforeAt + `@${file.filename} ` + textAfterCursor
      setInput(newText)

      // Set cursor position after the inserted filename
      const newCursorPos = beforeAt.length + file.filename.length + 2
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
          textareaRef.current.focus()
        }
      }, 0)
    }

    setShowAutocomplete(false)
  }

  if (!currentConversationId) {
    return null
  }

  return (
    <div ref={containerRef} className="border-t border-tertiary/30 bg-gray-900/40 backdrop-blur-sm relative">
      {/* Drag & Drop Overlay */}
      {isDragOver && (
        <div className="absolute inset-0 bg-primary/20 backdrop-blur-sm border-2 border-dashed border-primary z-50 flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-4">📁</div>
            <div className="text-xl font-semibold text-primary mb-2">Drop files here</div>
            <div className="text-sm text-supplement1">Images, PDFs, and text files supported</div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {/* File Attachments */}
          <FileAttachments
            attachedFiles={attachedFiles}
            onRemoveFile={(fileId) => {
              setAttachedFiles(prev => prev.filter(f => f.id !== fileId))
            }}
            onVectorizeFile={handleVectorizeFile}
          />

          <div className="flex items-center gap-2">
          {/* Attachment button */}
          <button
            ref={attachButtonRef}
            type="button"
            onClick={() => {
              console.log('Attachment button clicked')
              setShowAttachmentMenu(true)
            }}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            title="Attach file"
          >
            <Plus className="h-5 w-5 text-gray-300" />
          </button>

          {/* Context Vault Selector */}
          <CompactVaultSelector
            selectedContextId={selectedContextId || undefined}
            onContextChange={handleContextChange}
            className="flex-shrink-0"
          />

          {/* Input field */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Type your message… (Shift+Enter for new line, @filename to reference files)"
              className="u1-textarea w-full pr-20 min-h-[40px] max-h-32 resize-none"
              rows={1}
              disabled={isLoading}
            />

            {/* Character count */}
            {input.length > 0 && (
              <div className="absolute bottom-2 right-16 text-xs text-gray-500">
                {input.length}
              </div>
            )}
          </div>

          {/* Settings button */}
          <button
            type="button"
            onClick={() => setShowSettings(true)}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            title="Chat settings"
          >
            <Wrench className="h-5 w-5 text-gray-300" />
          </button>

          {/* Send button */}
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={`
              flex items-center justify-center w-10 h-10 rounded-lg transition-colors
              ${input.trim() && !isLoading
                ? 'bg-primary hover:bg-primary/80 text-gray-900'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
              }
            `}
            title="Send message"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </button>
          </div>
        </div>

        {/* Model selector and options */}
        <div className="max-w-4xl mx-auto mt-2 flex items-center justify-between text-xs text-neutral-500">
          <div className="flex items-center gap-4">
            <span>Model: {settings.selectedModel ?
              models.find(m => m.id === settings.selectedModel)?.name || settings.selectedModel
              : 'No model selected'}</span>
            <span>•</span>
            <span>Temp: {settings.temperature?.toFixed(1) || '0.7'}</span>
            <span>•</span>
            <span>Max: {settings.maxTokens?.toLocaleString() || '4K'}</span>
            {settings.topP && (
              <>
                <span>•</span>
                <span>Top-P: {settings.topP.toFixed(2)}</span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span>Shift+Enter for new line</span>
          </div>
        </div>
      </form>

      {/* Attachment Menu */}
      <AttachmentMenu
        isOpen={showAttachmentMenu}
        onClose={() => setShowAttachmentMenu(false)}
        onFileSelect={handleFileSelect}
        onImageSelect={handleImageSelect}
        anchorRef={attachButtonRef as React.RefObject<HTMLElement>}
      />

      {/* File Picker */}
      <FilePicker
        isOpen={showFilePicker}
        onClose={() => setShowFilePicker(false)}
        onFileSelect={handleFilesSelected}
        mode={filePickerMode}
      />

      {/* File Autocomplete */}
      <FileAutocomplete
        isOpen={showAutocomplete}
        query={autocompleteQuery}
        position={autocompletePosition}
        onSelect={handleFileAutocompleteSelect}
        onClose={() => setShowAutocomplete(false)}
      />

      {/* Chat Settings Drawer */}
      <ChatSettingsDrawer
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />


    </div>
  )
}

export default InputArea
