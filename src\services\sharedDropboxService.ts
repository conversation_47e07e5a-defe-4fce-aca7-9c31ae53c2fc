import { contextVaultService, SHARED_DROPBOX_CONTEXT_ID } from './contextVaultService'
import { BaseService, ServiceError, ServiceErrorCode } from './base'

export interface SharedDropboxFile {
  id: string
  filename: string
  filepath: string
  fileType: string
  fileSize: number
  uploadedAt: string
  processed: boolean
  extractedContent?: string
}

export interface UploadDestination {
  type: 'context' | 'shared'
  contextId?: string
  contextName?: string
  path: string
}

class SharedDropboxService extends BaseService {
  private readonly SHARED_FOLDER_NAME = 'shared-dropbox'
  private files: SharedDropboxFile[] = []
  private listeners: ((files: SharedDropboxFile[]) => void)[] = []

  constructor() {
    super({
      name: 'SharedDropboxService',
      autoInitialize: false // We'll initialize manually after electronAPI is available
    })
  }

  /**
   * Initialize shared dropbox - create folder structure if needed
   */
  protected async doInitialize(): Promise<void> {
    // Wait for electronAPI to be available
    await this.waitForElectronAPI()

    const vaultRoot = await this.getVaultRoot()
    if (!vaultRoot) {
      throw new ServiceError(
        ServiceErrorCode.CONFIGURATION_ERROR,
        'No vault root configured',
        { serviceName: this.serviceName, operation: 'doInitialize' }
      )
    }

    const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`

    // Create shared dropbox directory
    const createResult = await window.electronAPI.vault.createDirectory(sharedPath)
    if (!createResult.success) {
      throw new ServiceError(
        ServiceErrorCode.FILE_WRITE_ERROR,
        `Failed to create shared directory: ${createResult.error}`,
        { serviceName: this.serviceName, operation: 'doInitialize', details: { sharedPath } }
      )
    }

    // Create metadata file if it doesn't exist
    await this.createMetadataFile(sharedPath)

    // Load existing files
    await this.loadFiles()

    this.logger.info('Shared dropbox initialized successfully', 'doInitialize', { sharedPath })
  }

  /**
   * Wait for electronAPI to be available
   */
  private async waitForElectronAPI(): Promise<void> {
    if (window.electronAPI) {
      return
    }

    this.logger.debug('Waiting for electronAPI to be available', 'waitForElectronAPI')

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new ServiceError(
          ServiceErrorCode.TIMEOUT_ERROR,
          'electronAPI not available after timeout',
          { serviceName: this.serviceName, operation: 'waitForElectronAPI' }
        ))
      }, 10000) // 10 second timeout

      const interval = setInterval(() => {
        if (window.electronAPI) {
          clearInterval(interval)
          clearTimeout(timeout)
          resolve()
        }
      }, 100)
    })
  }

  /**
   * Create metadata file if it doesn't exist
   */
  private async createMetadataFile(sharedPath: string): Promise<void> {
    const metadataPath = `${sharedPath}/.metadata.json`
    const metadataExists = await window.electronAPI.vault.pathExists(metadataPath)

    if (!metadataExists.exists) {
      const metadata = {
        name: 'Shared Dropbox',
        description: 'Files uploaded when no context vault is selected',
        created: new Date().toISOString(),
        type: 'shared-dropbox'
      }

      const writeResult = await window.electronAPI.vault.writeFile(
        metadataPath,
        JSON.stringify(metadata, null, 2)
      )

      if (!writeResult.success) {
        throw new ServiceError(
          ServiceErrorCode.FILE_WRITE_ERROR,
          `Failed to create metadata file: ${writeResult.error}`,
          { serviceName: this.serviceName, operation: 'createMetadataFile', details: { metadataPath } }
        )
      }
    }
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    const vaultRoot = await this.getVaultRoot()
    if (!vaultRoot) {
      return false
    }

    const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
    const pathExists = await window.electronAPI.vault.pathExists(sharedPath)

    return pathExists.exists
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.files = []
    this.listeners = []
    this.logger.info('Shared dropbox service cleaned up', 'doCleanup')
  }

  /**
   * Public initialization method that waits for electronAPI
   */
  public async initialize(): Promise<void> {
    return super.initialize()
  }

  /**
   * Get vault root path
   */
  private async getVaultRoot(): Promise<string | null> {
    const result = await this.executeOperation(
      'getVaultRoot',
      async () => {
        const registry = await window.electronAPI.vault.getVaultRegistry()
        return registry?.vaultRoot || null
      }
    )

    if (!result.success) {
      this.logger.warn('Failed to get vault root, returning null', 'getVaultRoot', result.error)
      return null
    }

    return result.data!
  }

  /**
   * Determine upload destination based on current context selection
   */
  async getUploadDestination(): Promise<UploadDestination> {
    return await this.executeOperationOrThrow(
      'getUploadDestination',
      async () => {
        const selectedContextId = contextVaultService.getSelectedContextId()
        const selectedContext = contextVaultService.getSelectedContext()

        if (selectedContextId && selectedContextId !== SHARED_DROPBOX_CONTEXT_ID && selectedContext) {
          // Context vault selected - upload to context's actual path
          return {
            type: 'context' as const,
            contextId: selectedContext.id,
            contextName: selectedContext.name,
            path: selectedContext.path
          }
        } else {
          // No context selected or shared dropbox - upload to shared dropbox
          const vaultRoot = await this.getVaultRoot()
          if (!vaultRoot) {
            throw new ServiceError(
              ServiceErrorCode.CONFIGURATION_ERROR,
              'Vault root not found, cannot determine shared dropbox path',
              { serviceName: this.serviceName, operation: 'getUploadDestination' }
            )
          }
          const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`

          return {
            type: 'shared' as const,
            path: sharedPath
          }
        }
      }
    )
  }

  /**
   * Upload file to determined destination
   */
  async uploadFile(file: File): Promise<{ success: boolean; fileRecord?: SharedDropboxFile; error?: string }> {
    const result = await this.executeOperation(
      'uploadFile',
      async () => {
        const destination = await this.getUploadDestination()

        // Generate unique filename
        const timestamp = Date.now()
        const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
        const filename = `${timestamp}_${sanitizedName}`
        const filepath = `${destination.path}/${filename}`

        // Convert file to base64 for transfer
        const arrayBuffer = await file.arrayBuffer()
        const base64Content = Buffer.from(arrayBuffer).toString('base64')

        // Write file to destination
        const writeResult = await window.electronAPI.vault.writeFile(filepath, base64Content)
        if (!writeResult.success) {
          throw new ServiceError(
            ServiceErrorCode.FILE_WRITE_ERROR,
            `Failed to write file: ${writeResult.error}`,
            { serviceName: this.serviceName, operation: 'uploadFile', details: { filepath } }
          )
        }

        // Create file record
        const fileRecord: SharedDropboxFile = {
          id: `${timestamp}_${Math.random().toString(36).substring(2, 11)}`, // Fixed deprecated substr
          filename: file.name,
          filepath,
          fileType: file.type || 'unknown',
          fileSize: file.size,
          uploadedAt: new Date().toISOString(),
          processed: false
        }

        // If uploaded to shared dropbox, track it
        if (destination.type === 'shared') {
          this.files.push(fileRecord)
          this.notifyListeners()

          // Save updated file list
          await this.saveFileList()
        }

        this.logger.info(`File uploaded to ${destination.type}`, 'uploadFile', {
          filename: file.name,
          destination: destination.path,
          contextName: destination.contextName
        })

        return { success: true, fileRecord }
      },
      { filename: file.name, size: file.size }
    )

    if (!result.success) {
      return { success: false, error: result.error!.getUserMessage() }
    }

    return result.data!
  }

  /**
   * Load files from shared dropbox
   */
  private async loadFiles(): Promise<void> {
    const result = await this.executeOperation(
      'loadFiles',
      async () => {
        const vaultRoot = await this.getVaultRoot()
        if (!vaultRoot) {
          this.files = []
          return
        }

        const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
        const filesListPath = `${sharedPath}/.files.json`

        const fileExists = await window.electronAPI.vault.pathExists(filesListPath)
        if (!fileExists.exists) {
          this.files = []
          return
        }

        const readResult = await window.electronAPI.vault.readFile(filesListPath)
        if (readResult.success && readResult.content) {
          this.files = JSON.parse(readResult.content)
        } else {
          this.files = []
        }
      }
    )

    if (!result.success) {
      this.logger.warn('Failed to load files, using empty array', 'loadFiles', result.error)
      this.files = []
    }
  }

  /**
   * Save file list to shared dropbox
   */
  private async saveFileList(): Promise<void> {
    const result = await this.executeOperation(
      'saveFileList',
      async () => {
        const vaultRoot = await this.getVaultRoot()
        if (!vaultRoot) {
          throw new ServiceError(
            ServiceErrorCode.CONFIGURATION_ERROR,
            'No vault root configured',
            { serviceName: this.serviceName, operation: 'saveFileList' }
          )
        }

        const sharedPath = `${vaultRoot}/${this.SHARED_FOLDER_NAME}`
        const filesListPath = `${sharedPath}/.files.json`

        const writeResult = await window.electronAPI.vault.writeFile(
          filesListPath,
          JSON.stringify(this.files, null, 2)
        )

        if (!writeResult.success) {
          throw new ServiceError(
            ServiceErrorCode.FILE_WRITE_ERROR,
            `Failed to save file list: ${writeResult.error}`,
            { serviceName: this.serviceName, operation: 'saveFileList', details: { filesListPath } }
          )
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to save file list', 'saveFileList', result.error)
    }
  }

  /**
   * Get all shared dropbox files
   */
  getFiles(): SharedDropboxFile[] {
    return [...this.files]
  }

  /**
   * Subscribe to file changes
   */
  subscribe(listener: (files: SharedDropboxFile[]) => void): () => void {
    this.listeners.push(listener)
    listener(this.files) // Immediate call with current data
    
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.files))
  }

  /**
   * Remove file from shared dropbox
   */
  async removeFile(fileId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const fileIndex = this.files.findIndex(f => f.id === fileId)
      if (fileIndex === -1) {
        return { success: false, error: 'File not found' }
      }

      const file = this.files[fileIndex]
      
      // Remove physical file
      const removeResult = await window.electronAPI.vault.removeFile(file.filepath)
      if (!removeResult.success) {
        return { success: false, error: removeResult.error }
      }

      // Remove from tracking
      this.files.splice(fileIndex, 1)
      this.notifyListeners()
      
      // Save updated list
      await this.saveFileList()

      return { success: true }
    } catch (error) {
      console.error('[SharedDropbox] Failed to remove file:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get shared dropbox path
   */
  async getSharedPath(): Promise<string | null> {
    const vaultRoot = await this.getVaultRoot()
    return vaultRoot ? `${vaultRoot}/${this.SHARED_FOLDER_NAME}` : null
  }
}

export const sharedDropboxService = new SharedDropboxService()
