export interface Conversation {
  id: string
  title: string
  is_pinned: 0 | 1
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model?: string
  is_pinned?: 0 | 1
  entities?: string // JSON array of extracted entities
  topics?: string   // JSON array of identified topics
  processed_at?: string // Intelligence processing timestamp
  extraction_confidence?: number // Overall confidence score
  created_at: string
  attachments?: FileAttachment[]
}

export interface FileRecord {
  id: string
  filename: string
  filepath: string
  file_type: string
  mime_type?: string
  file_size: number
  content_hash: string
  extracted_content?: string
  metadata?: string // JSON
  created_at: string
  updated_at: string
}

export interface FileAttachment {
  id: string
  message_id: string
  file_id: string
  attachment_type: 'attachment' | 'reference'
  created_at: string
  file?: FileRecord // Populated when needed
}

export interface OpenRouterModel {
  id: string
  name: string
  description?: string
  context_length: number
  pricing: {
    prompt: string
    completion: string
  }
  top_provider: {
    max_completion_tokens?: number
  }
  // Enhanced properties for better categorization
  architecture?: {
    modality?: string
    tokenizer?: string
    instruct_type?: string
  }
  per_request_limits?: {
    prompt_tokens?: string
    completion_tokens?: string
  }
  // Model categorization
  categories?: string[]
}

export interface ChatCompletionRequest {
  model: string
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
  temperature?: number
  max_tokens?: number
  stream?: boolean
  // Enhanced sampling controls
  top_p?: number
  top_k?: number
  frequency_penalty?: number
  presence_penalty?: number
  stop?: string[]
}

export interface Settings {
  openRouterApiKey?: string
  selectedModel?: string
  temperature?: number
  maxTokens?: number
  theme?: 'dark' | 'light'
  // Enhanced model configuration
  topP?: number
  topK?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemPrompt?: string
  // Model selection preferences
  favoriteModels?: string[]
  modelFilter?: 'all' | 'free' | 'flagship' | 'reasoning' | 'code' | 'vision' | 'search'
}

export interface UpdateInfo {
  version: string
  releaseDate: string
  releaseName?: string
  releaseNotes?: string
}

export interface UpdateCheckResult {
  available: boolean
  message?: string
  error?: string
}

export interface UpdateDownloadResult {
  success: boolean
  error?: string
  message?: string
}

export interface UpdateProgress {
  percent: number
  bytesPerSecond: number
  total: number
  transferred: number
}

// Model categorization and filtering types
export interface ModelCategory {
  id: string
  name: string
  description: string
  filter: (model: OpenRouterModel) => boolean
  icon?: string
}

export interface ModelPreset {
  name: string
  description: string
  temperature: number
  topP: number
  topK: number
  maxTokens?: number
}

export interface EnhancedModelInfo extends OpenRouterModel {
  isFree: boolean
  isFlagship: boolean
  isReasoning: boolean
  isCode: boolean
  isVision: boolean
  isSearch: boolean
  provider: string
  maxTokensSupported: number
}

export interface DatabaseHealth {
  isHealthy: boolean
  integrityCheck: string
  version: number
  tableCount: number
  totalRecords: number
  fileSize: number
  lastBackup?: string
}

// Context Vault System Types
export interface ContextVault {
  id: string
  name: string
  path: string
  color: string
  icon: string
  created: string
  lastAccessed: string
  contexts: ContextFolder[]
}

export interface ContextFolder {
  id: string
  name: string
  path: string
  description: string
  color: string
  icon: string
  status: 'empty' | 'active' | 'growing' | 'archived'
  stats: {
    fileCount: number
    conversationCount: number
    lastModified: string
    sizeBytes: number
  }
  masterDoc: {
    exists: boolean
    path: string
    preview: string
    wordCount: number
    lastUpdated: string
  }
  aiInsights: {
    suggestedActions: string[]
    contextType: string
    readinessScore: number
  }
}

export interface VaultRegistry {
  version: string
  vaultRoot: string
  vaults: ContextVault[]
  lastScan: string
  preferences: {
    defaultVault: string | null
    defaultContext: string | null
    autoOrganize: boolean
    showEmptyHints: boolean
  }
}

export interface ContextVaultCard {
  id: string
  name: string
  description: string
  color: string
  icon: string
  status: 'empty' | 'active' | 'growing' | 'archived'
  fileCount: number
  conversationCount: number
  lastActivity: string
  masterPreview: string
  recentFiles: string[]
  suggestedActions: string[]
  contextType: string
  readinessScore: number
  quickActions: {
    primary: { label: string; action: string }
    secondary: { label: string; action: string }
  }
  // Additional properties used in UI
  category?: string
  chatCount?: number
}

export interface VaultTemplate {
  id: string
  name: string
  description: string
  icon: string
  structure: {
    folders: string[]
    files: { path: string; content: string }[]
  }
  masterDocTemplate: string
  contextType: string
}

// Intelligence System Types
export interface ExtractedEntity {
  text: string
  type: 'person' | 'place' | 'concept' | 'technology' | 'organization' | 'other'
  confidence: number
}

export interface ExtractedTopic {
  name: string
  relevance: number
  keywords: string[]
}

export interface ExtractedArtifact {
  type: 'code' | 'image' | 'document' | 'link' | 'other'
  title: string
  content_hash?: string
  description?: string
}

export interface IntelligenceExtractionData {
  entities: ExtractedEntity[]
  topics: ExtractedTopic[]
  artifacts: ExtractedArtifact[]
  summary?: string
}

export interface VaultAssignment {
  vault_id: string | null
  vault_path?: string
  assignment_method: 'suggested' | 'user_selected' | 'created_new' | 'skipped'
  suggestion_confidence?: number
  user_feedback?: 'accepted' | 'rejected' | 'modified'
}

export interface ProcessingMetadata {
  extraction_time_ms: number
  model_used: string
  processing_version: string
  error?: string
}

export interface DomainDetection {
  domain: string
  confidence: number
  keywords: string[]
}

export interface HybridExtractionInfo {
  confidence: number
  primaryDomain: DomainDetection
  needsUserReview: boolean
  enhancementUsed: boolean
  extractionMethod: 'keyword' | 'hybrid' | 'llm_only'
  qualityIndicators?: {
    entityDensity: number
    topicRelevance: number
    contentCoverage: number
  }
}

export interface PinnedIntelligence {
  id: string
  message_id: string
  extraction_data: IntelligenceExtractionData
  vault_assignment: VaultAssignment
  processing_metadata: ProcessingMetadata
  created_at: string
}

export interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  icon?: any
  color?: string
  children?: FileTreeNode[]
  size?: number
  modified?: string
}

// Re-export artifact types
export * from './artifacts'

// Electron API types
declare global {
  interface Window {
    electronAPI: {
      db: {
        getConversations: () => Promise<Conversation[]>
        getConversation: (id: string) => Promise<Conversation | null>
        createConversation: (title: string) => Promise<string>
        updateConversation: (id: string, title: string) => Promise<void>
        deleteConversation: (id: string) => Promise<void>
        addMessage: (conversationId: string, message: Omit<Message, 'id' | 'created_at'>) => Promise<string>
        getMessages: (conversationId: string) => Promise<Message[]>
        togglePinMessage: (messageId: string) => Promise<void>
        // Intelligence methods
        updateMessageIntelligence: (messageId: string, entities: string, topics: string, confidence: number) => Promise<void>
        addPinnedIntelligence: (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) => Promise<string>
        getPinnedIntelligence: (messageId: string) => Promise<any>
        getAllPinnedIntelligence: () => Promise<any[]>
        searchConversations: (searchTerm: string) => Promise<Conversation[]>
        getConversationsWithArtifacts: () => Promise<Conversation[]>
        getArtifacts: (messageId: string) => Promise<any[]>
        addArtifact: (messageId: string, artifact: any) => Promise<string>
        updateArtifact: (id: string, updates: any) => Promise<void>
        removeArtifact: (id: string) => Promise<void>
        getConversationArtifacts: (conversationId: string) => Promise<any[]>
        // Database diagnostics
        getDatabaseHealth: () => Promise<DatabaseHealth>
        createBackup: () => Promise<string>
      }
      settings: {
        get: (key: string) => Promise<any>
        set: (key: string, value: any) => Promise<void>
      }
      files: {
        getIndexedFiles: () => Promise<FileRecord[]>
        searchFiles: (query: string, limit?: number) => Promise<FileRecord[]>
        processFileContent: (fileId: string) => Promise<boolean>
        indexFile: (filePath: string, processContent?: boolean) => Promise<string | null>
        // File attachment operations
        addFileAttachment: (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => Promise<string>
        getFileAttachments: (messageId: string) => Promise<any[]>
        getMessageFiles: (messageId: string) => Promise<any[]>
        removeFileAttachment: (attachmentId: string) => Promise<void>
        deleteFile: (fileId: string) => Promise<boolean>
        getFileContent: (filePath: string) => Promise<Buffer | null>
        fileExists: (filePath: string) => Promise<boolean>
        showOpenDialog: (options: any) => Promise<any>
        showSaveDialog: (options: any) => Promise<any>
        // File diagnostics
        diagnoseFileProcessing: (filePath: string) => Promise<any>
        // File operations
        saveFile: (filename: string, base64Content: string) => Promise<string>
      }
      updater: {
        checkForUpdates: () => Promise<UpdateCheckResult>
        downloadAndInstall: () => Promise<UpdateDownloadResult>
        onCheckingForUpdate: (callback: () => void) => void
        onUpdateAvailable: (callback: (info: UpdateInfo) => void) => void
        onUpdateNotAvailable: (callback: () => void) => void
        onError: (callback: (error: string) => void) => void
        onDownloadProgress: (callback: (progress: UpdateProgress) => void) => void
        onUpdateDownloaded: (callback: (info: UpdateInfo) => void) => void
      }
      vault: {
        createDirectory: (dirPath: string) => Promise<{ success: boolean; error?: string }>
        writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>
        readDirectory: (dirPath: string) => Promise<{
          success: boolean;
          error?: string;
          items: Array<{
            name: string;
            path: string;
            isDirectory: boolean;
            size?: number;
            modified: string;
          }>
        }>
        removeDirectory: (dirPath: string) => Promise<{ success: boolean; error?: string }>
        removeFile: (filePath: string) => Promise<{ success: boolean; error?: string }>
        scanFolder: (folderPath: string) => Promise<{
          success: boolean;
          files?: Array<{
            name: string;
            path: string;
            isDirectory: boolean;
            size: number;
            lastModified: string;
          }>;
          error?: string
        }>
        copyFile: (sourcePath: string, destinationPath: string) => Promise<{ success: boolean; error?: string }>
        pathExists: (targetPath: string) => Promise<{ exists: boolean; error?: string }>
        readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>
        getVaultRegistry: () => Promise<VaultRegistry | null>
        saveVaultRegistry: (registry: VaultRegistry) => Promise<{ success: boolean; error?: string }>
        initializeVaultRoot: (rootPath: string, template: string) => Promise<{ success: boolean; vaults: ContextVault[]; error?: string }>
        scanContexts: (vaultPath: string) => Promise<{ success: boolean; contexts: ContextFolder[]; error?: string }>
      }
      // Window controls
      windowControls?: {
        minimize: () => void
        maximize: () => void
        close: () => void
      }
      // Shell operations
      shell?: {
        openExternal: (url: string) => Promise<void>
      }
      // Plugin management
      plugins: {
        getAll: () => Promise<any[]>
        enable: (pluginId: string, enabled: boolean) => Promise<void>
        disable: (pluginId: string) => Promise<void>
        discover: () => Promise<void>
        getConfig: (pluginId: string) => Promise<any>
        updateConfig: (pluginId: string, config: any) => Promise<void>
        getCapabilities: () => Promise<any>
        getAPIEndpoints: (pluginId: string) => Promise<{ success: boolean; namespace?: string; endpoints?: any[]; error?: string }>
        getAllAPIEndpoints: () => Promise<{ success: boolean; apiInfo?: any[]; error?: string }>
      }
      // Generic IPC invoke method for plugin APIs
      invoke: (channel: string, ...args: any[]) => Promise<any>
    }
  }
}
